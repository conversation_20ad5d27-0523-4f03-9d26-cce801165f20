import requests
import json
import time
from bs4 import BeautifulSoup

def scrape_baidu(query,pages):
  base_url='https://www.baidu.com/s?wd={}&pn={}'
  headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  }
  all_results=[]
  for i in range(pages):
    page_num=i+1
    pn_value=i*10
    url=base_url.format(query,pn_value)
    try:
      r=requests.get(url,timeout=30)
      r.encoding=r.apparent_encoding
      r.raise_for_status
      soup=BeautifulSoup(r.text,'html.parser')

      containers=soup.find_all('div',class_=lambda v:v and 'c-container' in v)
      for container in containers:
        title_tag=container.find('h3')
        abstract_tag=container.find('div',class_='c-abstract')
        if title_tag:
          title=title_tag.text.strip()
          link=title_tag.find('a').get('href','未找到链接') if title_tag.find('a') else '未找到链接'
          abstract=abstract_tag.text.strip() if abstract_tag else '未找到摘要'
          result={'title':title,'link':link,'abstract':abstract}
          all_results.append(result)
        time.sleep(1)
    except requests.RequestException as e:
      print(e)
      continue
    output_filename=f'{query}_result.json'
    with open(output_filename,'w',encoding='utf-8') as f:
      json.dump(all_results,f,ensure_ascii=False,indent=4)
    print(f'已保存{len(all_results)}条结果到{output_filename}')

scrape_baidu('Deepseek',10)