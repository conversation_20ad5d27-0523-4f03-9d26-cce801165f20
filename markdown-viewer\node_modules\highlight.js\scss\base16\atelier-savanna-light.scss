pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Atelier Savanna Light
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme atelier-savanna-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #ecf4ee  Default Background
base01  #dfe7e2  Lighter Background (Used for status bars, line number and folding marks)
base02  #87928a  Selection Background
base03  #78877d  Comments, Invisibles, Line Highlighting
base04  #5f6d64  Dark Foreground (Used for status bars)
base05  #526057  Default Foreground, Caret, Delimiters, Operators
base06  #232a25  Light Foreground (Not often used)
base07  #171c19  Light Background (Not often used)
base08  #b16139  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #9f713c  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #a07e3b  Classes, Markup Bold, Search Text Background
base0B  #489963  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #1c9aa0  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #478c90  Functions, Methods, Attribute IDs, Headings
base0E  #55859b  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #867469  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #526057;
  background: #ecf4ee
}
.hljs::selection,
.hljs ::selection {
  background-color: #87928a;
  color: #526057
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #78877d -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #78877d
}
/* base04 - #5f6d64 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #5f6d64
}
/* base05 - #526057 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #526057
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #b16139
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #9f713c
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #a07e3b
}
.hljs-strong {
  font-weight: bold;
  color: #a07e3b
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #489963
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #1c9aa0
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #478c90
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #55859b
}
.hljs-emphasis {
  color: #55859b;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #867469
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}