pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Mellow Purple
  Author: gidsi
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme mellow-purple
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1e0528  Default Background
base01  #1A092D  Lighter Background (Used for status bars, line number and folding marks)
base02  #331354  Selection Background
base03  #320f55  Comments, Invisibles, Line Highlighting
base04  #873582  Dark Foreground (Used for status bars)
base05  #ffeeff  Default Foreground, Caret, Delimiters, Operators
base06  #ffeeff  Light Foreground (Not often used)
base07  #f8c0ff  Light Background (Not often used)
base08  #00d9e9  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #aa00a3  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #955ae7  Classes, Markup Bold, Search Text Background
base0B  #05cb0d  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #b900b1  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #550068  Functions, Methods, Attribute IDs, Headings
base0E  #8991bb  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #4d6fff  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #ffeeff;
  background: #1e0528
}
.hljs::selection,
.hljs ::selection {
  background-color: #331354;
  color: #ffeeff
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #320f55 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #320f55
}
/* base04 - #873582 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #873582
}
/* base05 - #ffeeff -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #ffeeff
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #00d9e9
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #aa00a3
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #955ae7
}
.hljs-strong {
  font-weight: bold;
  color: #955ae7
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #05cb0d
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #b900b1
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #550068
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #8991bb
}
.hljs-emphasis {
  color: #8991bb;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #4d6fff
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}