pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Darcula
  Author: jetbrains
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme darcula
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #2b2b2b  Default Background
base01  #323232  Lighter Background (Used for status bars, line number and folding marks)
base02  #323232  Selection Background
base03  #606366  Comments, Invisibles, Line Highlighting
base04  #a4a3a3  Dark Foreground (Used for status bars)
base05  #a9b7c6  Default Foreground, Caret, Delimiters, Operators
base06  #ffc66d  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #4eade5  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #689757  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #bbb529  Classes, Markup Bold, Search Text Background
base0B  #6a8759  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #629755  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #9876aa  Functions, Methods, Attribute IDs, Headings
base0E  #cc7832  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #808080  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #a9b7c6;
  background: #2b2b2b
}
.hljs::selection,
.hljs ::selection {
  background-color: #323232;
  color: #a9b7c6
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #606366 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #606366
}
/* base04 - #a4a3a3 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #a4a3a3
}
/* base05 - #a9b7c6 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #a9b7c6
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #4eade5
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #689757
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #bbb529
}
.hljs-strong {
  font-weight: bold;
  color: #bbb529
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #6a8759
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #629755
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #9876aa
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #cc7832
}
.hljs-emphasis {
  color: #cc7832;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #808080
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}