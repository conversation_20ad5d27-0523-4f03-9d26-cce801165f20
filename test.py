import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def sigmoid(x):
    """Sigmoid激活函数"""
    return 1 / (1 + np.exp(-np.clip(x, -500, 500)))  # 防止溢出

def swish(x, beta=1.0):
    """Swish激活函数"""
    return x * sigmoid(beta * x)

def relu(x):
    """ReLU激活函数"""
    return np.maximum(0, x)

def swiglu_simplified(x):
    """
    简化版SwiGLU函数
    这里我们假设输入x被分成两部分：x1和x2
    SwiGLU(x) = x1 * Swish(x2)
    为了演示，我们使用 x1 = x, x2 = x
    """
    return x * swish(x)

def swiglu_variant(x):
    """
    另一种SwiGLU变体
    模拟更真实的门控效果：x1 = x, x2 = 0.5*x + 0.1
    """
    x1 = x
    x2 = 0.5 * x + 0.1
    return x1 * swish(x2)

# 生成数据点
x = np.linspace(-3, 3, 1000)

# 计算各函数值
y_relu = relu(x)
y_swish = swish(x)
y_swiglu_simple = swiglu_simplified(x)
y_swiglu_variant = swiglu_variant(x)

# 创建图形
fig, axes = plt.subplots(2, 2, figsize=(12, 10))
fig.suptitle('激活函数对比：ReLU vs SwiGLU', fontsize=16, fontweight='bold')

# 1. ReLU vs Swish
axes[0, 0].plot(x, y_relu, 'r-', linewidth=2, label='ReLU(x)')
axes[0, 0].plot(x, y_swish, 'b-', linewidth=2, label='Swish(x)')
axes[0, 0].set_title('ReLU vs Swish', fontsize=12)
axes[0, 0].set_xlabel('x')
axes[0, 0].set_ylabel('f(x)')
axes[0, 0].grid(True, alpha=0.3)
axes[0, 0].legend()
axes[0, 0].axhline(y=0, color='k', linewidth=0.5)
axes[0, 0].axvline(x=0, color='k', linewidth=0.5)

# 2. ReLU vs SwiGLU (简化版)
axes[0, 1].plot(x, y_relu, 'r-', linewidth=2, label='ReLU(x)')
axes[0, 1].plot(x, y_swiglu_simple, 'g-', linewidth=2, label='SwiGLU简化版')
axes[0, 1].set_title('ReLU vs SwiGLU(简化版)', fontsize=12)
axes[0, 1].set_xlabel('x')
axes[0, 1].set_ylabel('f(x)')
axes[0, 1].grid(True, alpha=0.3)
axes[0, 1].legend()
axes[0, 1].axhline(y=0, color='k', linewidth=0.5)
axes[0, 1].axvline(x=0, color='k', linewidth=0.5)

# 3. 不同SwiGLU变体对比
axes[1, 0].plot(x, y_swiglu_simple, 'g-', linewidth=2, label='SwiGLU: x·Swish(x)')
axes[1, 0].plot(x, y_swiglu_variant, 'm-', linewidth=2, label='SwiGLU: x·Swish(0.5x+0.1)')
axes[1, 0].set_title('不同SwiGLU变体对比', fontsize=12)
axes[1, 0].set_xlabel('x')
axes[1, 0].set_ylabel('f(x)')
axes[1, 0].grid(True, alpha=0.3)
axes[1, 0].legend()
axes[1, 0].axhline(y=0, color='k', linewidth=0.5)
axes[1, 0].axvline(x=0, color='k', linewidth=0.5)

# 4. 所有函数总览
axes[1, 1].plot(x, y_relu, 'r-', linewidth=2, label='ReLU(x)')
axes[1, 1].plot(x, y_swish, 'b-', linewidth=2, label='Swish(x)')
axes[1, 1].plot(x, y_swiglu_simple, 'g-', linewidth=2, label='SwiGLU简化版')
axes[1, 1].set_title('所有函数对比', fontsize=12)
axes[1, 1].set_xlabel('x')
axes[1, 1].set_ylabel('f(x)')
axes[1, 1].grid(True, alpha=0.3)
axes[1, 1].legend()
axes[1, 1].axhline(y=0, color='k', linewidth=0.5)
axes[1, 1].axvline(x=0, color='k', linewidth=0.5)

plt.tight_layout()
plt.show()

# 打印一些关键点的函数值
print("关键点的函数值对比：")
print("-" * 50)
test_points = [-2, -1, 0, 1, 2]
for point in test_points:
    print(f"x = {point:2d}:")
    print(f"  ReLU({point}) = {relu(point):8.4f}")
    print(f"  Swish({point}) = {swish(point):8.4f}")
    print(f"  SwiGLU({point}) = {swiglu_simplified(point):8.4f}")
    print()

# 绘制导数对比（可选）
def plot_derivatives():
    """绘制函数导数对比"""
    # 数值计算导数
    dx = x[1] - x[0]
    dy_relu = np.gradient(y_relu, dx)
    dy_swish = np.gradient(y_swish, dx)
    dy_swiglu = np.gradient(y_swiglu_simple, dx)
    
    plt.figure(figsize=(10, 6))
    plt.plot(x, dy_relu, 'r-', linewidth=2, label="ReLU'(x)")
    plt.plot(x, dy_swish, 'b-', linewidth=2, label="Swish'(x)")
    plt.plot(x, dy_swiglu, 'g-', linewidth=2, label="SwiGLU'(x)")
    plt.title('激活函数导数对比', fontsize=14, fontweight='bold')
    plt.xlabel('x')
    plt.ylabel("f'(x)")
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.axhline(y=0, color='k', linewidth=0.5)
    plt.axvline(x=0, color='k', linewidth=0.5)
    plt.tight_layout()
    plt.show()

# 取消注释下面这行来查看导数对比
plot_derivatives()