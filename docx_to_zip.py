import os
import sys
import zipfile
def docx_to_zip(src_path,dest_path):
    if not os.path.isfile(src_path):
        raise Exception(f"文件{src_path}不存在")
        #raise FileNotFoundError(f"文件{src_path}不存在")
    if not os.path.exists(dest_path):
        os.makedirs(dest_path)
    count=0
    with zipfile.ZipFile(dest_path,'w') as zf:
        for dirpath,dirnames,filenames in os.walk(src_path):
            for filename in filenames:
                if filename.lower().endswith('.docx'):
                    file_to_zip=os.path.join(dirpath,filename)
                    arcname=os.path.relpath(file_to_zip,src_path)
                    zf.write(file_to_zip,arcname)
                    count+=1
    return count

if __name__ == "__main__":
    source_directory = 'documents_to_zip'
    output_zip_file = 'docx_archive.zip'
    
    # --- 为方便测试，自动创建一些测试文件和目录 ---
    if not os.path.exists(source_directory):
        os.makedirs(os.path.join(source_directory, 'subdir'))
    with open(os.path.join(source_directory, 'report.docx'), 'w') as f: pass
    with open(os.path.join(source_directory, 'subdir', 'proposal.docx'), 'w') as f: pass
    with open(os.path.join(source_directory, 'notes.txt'), 'w') as f: pass
    # --- 测试文件创建完毕 ---

    count = docx_to_zip(source_directory, output_zip_file)
    
    if count > 0:
        print(f"\n任务完成：成功将 {count} 个.docx文件压缩到 '{output_zip_file}'")
    else:
        print("\n未找到任何.docx文件或源目录不存在。")