pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*
  xt256.css

  Contact: initbar [at] protonmail [dot] ch
         : github.com/initbar
*/
.hljs {
  color: #eaeaea;
  background: #000
}
.hljs-subst {
  color: #eaeaea
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}
.hljs-type {
  color: #eaeaea
}
.hljs-params {
  color: #da0000
}
.hljs-literal,
.hljs-number,
.hljs-name {
  color: #ff0000;
  font-weight: bolder
}
.hljs-comment {
  color: #969896
}
.hljs-selector-id,
.hljs-quote {
  color: #00ffff
}
.hljs-template-variable,
.hljs-variable,
.hljs-title {
  color: #00ffff;
  font-weight: bold
}
.hljs-selector-class,
.hljs-keyword,
.hljs-symbol {
  color: #fff000
}
.hljs-string,
.hljs-bullet {
  color: #00ff00
}
.hljs-tag,
.hljs-section {
  color: #000fff
}
.hljs-selector-tag {
  color: #000fff;
  font-weight: bold
}
.hljs-attribute,
.hljs-built_in,
.hljs-regexp,
.hljs-link {
  color: #ff00ff
}
.hljs-meta {
  color: #fff;
  font-weight: bolder
}