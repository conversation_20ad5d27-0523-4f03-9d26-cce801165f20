# Security Policy

Due to both time and resource constrains the Highlight.js core team fully supports only the current major/minor release of the library.  Prior major releases may be supported for a short time after new major releases are issued.  Problems with minor releases are often resolved by upgrading to the most recent minor release.

### Release Status

| Version    | Support | Status  |
| :-----:    | :-: | :------ |
| 11.x       | :white_check_mark: |  The 11.x series recieves regular updates, new features & security fixes. |
| 10.7.x     | :x: |  No longer supported. <br>See [VERSION_11_UPGRADE.md](https://github.com/highlightjs/highlight.js/blob/master/VERSION_11_UPGRADE.md).|
| <= 10.4.0  | :x: | Known vulnerabities. |
| <= 9.18.5  | :x: | Known vulnerabities. [EOL](https://github.com/highlightjs/highlight.js/issues/2877) |
| 7.x, 8.x   | :x: | Obsolete. Known vulnerabities. |


### Reporting a Vulnerability

Many vulnerabilities can simply be reported (and tracked) via our [GitHub issues](https://github.com/highlightjs/highlight.js/issues).   If you feel your issue is more sensitive than that you can always reach us via email: [<EMAIL>](mailto:<EMAIL>)

