import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

def sigmoid(x):
    """Sigmoid激活函数"""
    return 1 / (1 + np.exp(-np.clip(x, -500, 500)))

def swish(x, beta=1.0):
    """Swish激活函数: x * sigmoid(β*x)"""
    return x * sigmoid(beta * x)

def relu(x):
    """ReLU激活函数: max(0, x)"""
    return np.maximum(0, x)

def swiglu_simplified(x):
    """简化版SwiGLU: x * swish(x)"""
    return x * swish(x)

def swiglu_gated(x, gate_weight=0.5, gate_bias=0.1):
    """门控版SwiGLU: x * swish(gate_weight*x + gate_bias)"""
    return x * swish(gate_weight * x + gate_bias)

# 生成数据
x = np.linspace(-4, 4, 1000)
y_relu = relu(x)
y_swish = swish(x)
y_swiglu_simple = swiglu_simplified(x)
y_swiglu_gated = swiglu_gated(x)

# 创建交互式图表
fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=('ReLU vs Swish 基础对比', 'ReLU vs SwiGLU 对比', 
                   '不同SwiGLU变体', '所有函数总览'),
    vertical_spacing=0.12,
    horizontal_spacing=0.1
)

# 子图1: ReLU vs Swish
fig.add_trace(
    go.Scatter(x=x, y=y_relu, name='ReLU', line=dict(color='red', width=3)),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=x, y=y_swish, name='Swish', line=dict(color='blue', width=3)),
    row=1, col=1
)

# 子图2: ReLU vs SwiGLU
fig.add_trace(
    go.Scatter(x=x, y=y_relu, name='ReLU', line=dict(color='red', width=3), 
               showlegend=False),
    row=1, col=2
)
fig.add_trace(
    go.Scatter(x=x, y=y_swiglu_simple, name='SwiGLU(简化)', 
               line=dict(color='green', width=3)),
    row=1, col=2
)

# 子图3: 不同SwiGLU变体
fig.add_trace(
    go.Scatter(x=x, y=y_swiglu_simple, name='SwiGLU: x·Swish(x)', 
               line=dict(color='green', width=3), showlegend=False),
    row=2, col=1
)
fig.add_trace(
    go.Scatter(x=x, y=y_swiglu_gated, name='SwiGLU: x·Swish(0.5x+0.1)', 
               line=dict(color='purple', width=3)),
    row=2, col=1
)

# 子图4: 所有函数
fig.add_trace(
    go.Scatter(x=x, y=y_relu, name='ReLU', line=dict(color='red', width=2), 
               showlegend=False),
    row=2, col=2
)
fig.add_trace(
    go.Scatter(x=x, y=y_swish, name='Swish', line=dict(color='blue', width=2), 
               showlegend=False),
    row=2, col=2
)
fig.add_trace(
    go.Scatter(x=x, y=y_swiglu_simple, name='SwiGLU', line=dict(color='green', width=2), 
               showlegend=False),
    row=2, col=2
)

# 更新布局
fig.update_layout(
    title_text="激活函数对比：ReLU vs SwiGLU (交互式版本)",
    title_x=0.5,
    title_font_size=18,
    height=800,
    width=1200,
    showlegend=True,
    hovermode='x unified',
    template='plotly_white'
)

# 更新坐标轴
for i in range(1, 3):
    for j in range(1, 3):
        fig.update_xaxes(title_text="x", gridcolor='lightgray', row=i, col=j)
        fig.update_yaxes(title_text="f(x)", gridcolor='lightgray', row=i, col=j)

# 显示图表
fig.show()

# 创建单独的详细对比图
def create_detailed_comparison():
    """创建详细的函数对比图"""
    fig_detail = go.Figure()
    
    # 添加所有函数
    fig_detail.add_trace(go.Scatter(
        x=x, y=y_relu, name='ReLU(x) = max(0, x)',
        line=dict(color='red', width=3),
        hovertemplate='<b>ReLU</b><br>x: %{x:.2f}<br>y: %{y:.4f}<extra></extra>'
    ))
    
    fig_detail.add_trace(go.Scatter(
        x=x, y=y_swish, name='Swish(x) = x·σ(x)',
        line=dict(color='blue', width=3),
        hovertemplate='<b>Swish</b><br>x: %{x:.2f}<br>y: %{y:.4f}<extra></extra>'
    ))
    
    fig_detail.add_trace(go.Scatter(
        x=x, y=y_swiglu_simple, name='SwiGLU(x) = x·Swish(x)',
        line=dict(color='green', width=3),
        hovertemplate='<b>SwiGLU</b><br>x: %{x:.2f}<br>y: %{y:.4f}<extra></extra>'
    ))
    
    fig_detail.update_layout(
        title={
            'text': "激活函数详细对比<br><sub>将鼠标悬停在曲线上查看具体数值</sub>",
            'x': 0.5,
            'font': {'size': 20}
        },
        xaxis_title="输入值 (x)",
        yaxis_title="输出值 f(x)",
        width=900,
        height=600,
        hovermode='x unified',
        template='plotly_white',
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01,
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="black",
            borderwidth=1
        )
    )
    
    # 添加零线
    fig_detail.add_hline(y=0, line_dash="dash", line_color="gray", opacity=0.5)
    fig_detail.add_vline(x=0, line_dash="dash", line_color="gray", opacity=0.5)
    
    return fig_detail

# 显示详细对比图
detailed_fig = create_detailed_comparison()
detailed_fig.show()

# 数值对比表格
print("=" * 60)
print("激活函数在关键点的数值对比")
print("=" * 60)
print(f"{'x':<8} {'ReLU(x)':<12} {'Swish(x)':<12} {'SwiGLU(x)':<12}")
print("-" * 60)

test_points = [-3, -2, -1, -0.5, 0, 0.5, 1, 2, 3]
for point in test_points:
    relu_val = relu(point)
    swish_val = swish(point)
    swiglu_val = swiglu_simplified(point)
    print(f"{point:<8.1f} {relu_val:<12.4f} {swish_val:<12.4f} {swiglu_val:<12.4f}")

print("\n" + "=" * 60)
print("函数特性分析：")
print("• ReLU: 在x>0时线性增长，x≤0时为0，存在'死神经元'问题")
print("• Swish: 平滑非线性，在x≈-1.28时有最小值，具有自门控特性")
print("• SwiGLU: 结合门控机制，在大模型中表现优于ReLU")
print("=" * 60)