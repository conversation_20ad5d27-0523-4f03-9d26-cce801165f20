# 示例 Markdown 文档

这是一个示例 Markdown 文档，展示了各种 Markdown 语法的使用。

## 文本格式

**粗体文本** 和 *斜体文本*

~~删除线文本~~

`行内代码`

## 标题层级

### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题

## 列表

### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

### 有序列表
1. 第一项
2. 第二项
3. 第三项

### 任务列表
- [x] 已完成的任务
- [ ] 待完成的任务
- [x] 另一个已完成的任务
- [ ] 另一个待完成的任务

## 代码块

### JavaScript
```javascript
function greet(name) {
  console.log(`Hello, ${name}!`);
}

greet('World');
```

### Python
```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibon<PERSON>ci(10))
```

### HTML
```html
<!DOCTYPE html>
<html>
<head>
    <title>示例页面</title>
</head>
<body>
    <h1>Hello World</h1>
</body>
</html>
```

## 表格

| 功能 | 状态 | 优先级 | 备注 |
|------|------|--------|------|
| Markdown 解析 | ✅ 完成 | 高 | 基础功能 |
| 代码高亮 | ✅ 完成 | 高 | 支持多语言 |
| 表格支持 | ✅ 完成 | 中 | GFM 扩展 |
| 文件上传 | ✅ 完成 | 中 | 拖拽支持 |
| 主题切换 | ⏳ 进行中 | 低 | 未来功能 |
| 导出功能 | 📋 计划中 | 低 | 未来功能 |

## 引用

> 这是一个引用示例。
> 
> 引用可以包含多行文本，
> 并且可以嵌套其他 Markdown 元素。
> 
> > 这是嵌套引用。

## 链接和图片

[GitHub](https://github.com) - 世界最大的代码托管平台

[相对链接](./sample.md)

## 分隔线

---

## 数学公式（如果支持）

行内公式：$E = mc^2$

块级公式：
$$
\sum_{i=1}^{n} x_i = x_1 + x_2 + \cdots + x_n
$$

## 特殊字符

&copy; 2024 Markdown 展示器

HTML 实体：&lt;script&gt;alert('XSS')&lt;/script&gt;

## 脚注（如果支持）

这里有一个脚注引用[^1]。

[^1]: 这是脚注的内容。

## 总结

这个 Markdown 展示器支持：

1. ✅ 标准 Markdown 语法
2. ✅ GitHub Flavored Markdown (GFM)
3. ✅ 代码语法高亮
4. ✅ 表格支持
5. ✅ 任务列表
6. ✅ 文件上传和拖拽
7. ✅ 响应式设计

试试上传你自己的 `.md` 文件，或者在左侧编辑器中编写你的 Markdown 内容！
