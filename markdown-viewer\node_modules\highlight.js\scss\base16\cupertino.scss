pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Cupertino
  Author: Defman21
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme cupertino
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #ffffff  Default Background
base01  #c0c0c0  Lighter Background (Used for status bars, line number and folding marks)
base02  #c0c0c0  Selection Background
base03  #808080  Comments, Invisibles, Line Highlighting
base04  #808080  Dark Foreground (Used for status bars)
base05  #404040  Default Foreground, Caret, Delimiters, Operators
base06  #404040  Light Foreground (Not often used)
base07  #5e5e5e  Light Background (Not often used)
base08  #c41a15  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #eb8500  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #826b28  Classes, Markup Bold, Search Text Background
base0B  #007400  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #318495  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #0000ff  Functions, Methods, Attribute IDs, Headings
base0E  #a90d91  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #826b28  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #404040;
  background: #ffffff
}
.hljs::selection,
.hljs ::selection {
  background-color: #c0c0c0;
  color: #404040
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #808080 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #808080
}
/* base04 - #808080 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #808080
}
/* base05 - #404040 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #404040
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #c41a15
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #eb8500
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #826b28
}
.hljs-strong {
  font-weight: bold;
  color: #826b28
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #007400
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #318495
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #0000ff
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #a90d91
}
.hljs-emphasis {
  color: #a90d91;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #826b28
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}