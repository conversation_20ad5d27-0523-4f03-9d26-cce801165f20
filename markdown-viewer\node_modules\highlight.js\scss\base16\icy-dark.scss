pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Icy Dark
  Author: icyphox (https://icyphox.ga)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme icy-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #021012  Default Background
base01  #031619  Lighter Background (Used for status bars, line number and folding marks)
base02  #041f23  Selection Background
base03  #052e34  Comments, Invisibles, Line Highlighting
base04  #064048  Dark Foreground (Used for status bars)
base05  #095b67  Default Foreground, Caret, Delimiters, Operators
base06  #0c7c8c  Light Foreground (Not often used)
base07  #109cb0  Light Background (Not often used)
base08  #16c1d9  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #b3ebf2  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #80deea  Classes, Markup Bold, Search Text Background
base0B  #4dd0e1  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #26c6da  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #00bcd4  Functions, Methods, Attribute IDs, Headings
base0E  #00acc1  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #0097a7  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #095b67;
  background: #021012
}
.hljs::selection,
.hljs ::selection {
  background-color: #041f23;
  color: #095b67
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #052e34 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #052e34
}
/* base04 - #064048 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #064048
}
/* base05 - #095b67 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #095b67
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #16c1d9
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #b3ebf2
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #80deea
}
.hljs-strong {
  font-weight: bold;
  color: #80deea
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #4dd0e1
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #26c6da
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #00bcd4
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #00acc1
}
.hljs-emphasis {
  color: #00acc1;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #0097a7
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}