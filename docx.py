"""
请编写一个Python脚本，实现一个函数 generate_reports(data_dir, report_dir)，功能如下：

遍历 data_dir 目录下的所有 .txt 文件。
对于每个 .txt 文件，将其内容解析出来。
使用 python-docx 库为每位学生生成一个独立的 .docx 格式的报告。
报告的文件名应与源文件名相同（例如，张三.docx），并存放在 report_dir 目录下（如果该目录不存在，则自动创建）。
报告内容应包含：
一个主标题，内容为“XX的成绩单”（XX为学生姓名）。
一个段落，列出所有科目及其成绩。
"""



import docx
import os
from docx import Document
def generate_reports(data_dir,report_dir):
    if not os.path.exists(report_dir):
        os.makedirs(report_dir)
    if not os.path.exists(data_dir):
        raise Exception(f"目录{data_dir}不存在")
    for filename in os.listdir(data_dir):
        if filename.lower().endswith('.txt'):
            file_path=os.path.join(data_dir,filename)
            student_name=os.path.splitext(filename)[0]
            doc=Document()
            doc.add_heading(f"{student_name}的成绩单",level=1)
            try:
                with open(file_path,'r',encoding='utf-8') as file:
                    for line in file:
                        if line.strip():
                            doc.add_paragraph(line.strip())
                report_filepath = os.path.join(report_dir, f"{student_name}.docx")
                doc.save(report_filepath)
                print(f"成功生成报告: {report_filepath}")
            except Exception as e:
                print(f"生成报告时出错: {e}")
if __name__ == "__main__":
    data_directory = 'student_scores'
    report_directory = 'generated_reports'

    # --- 为方便测试，自动创建一些测试文件和目录 ---
    if not os.path.exists(data_directory):
        os.makedirs(data_directory)
    with open(os.path.join(data_directory, '张三.txt'), 'w', encoding='utf-8') as f:
        f.write('语文:95\n数学:102\n英语:88\n')
    with open(os.path.join(data_directory, '李四.txt'), 'w', encoding='utf-8') as f:
        f.write('语文:85\n数学:98\n综合:92\n')
    # --- 测试文件创建完毕 ---
    
    generate_reports(data_directory, report_directory)