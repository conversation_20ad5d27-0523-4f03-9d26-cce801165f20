pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Dracula
  Author: <PERSON> (http://github.com/mikebarkmin) based on Dracula Theme (http://github.com/dracula)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme dracula
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #282936  Default Background
base01  #3a3c4e  Lighter Background (Used for status bars, line number and folding marks)
base02  #4d4f68  Selection Background
base03  #626483  Comments, Invisibles, Line Highlighting
base04  #62d6e8  Dark Foreground (Used for status bars)
base05  #e9e9f4  Default Foreground, Caret, Delimiters, Operators
base06  #f1f2f8  Light Foreground (Not often used)
base07  #f7f7fb  Light Background (Not often used)
base08  #ea51b2  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #b45bcf  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #00f769  Classes, Markup Bold, Search Text Background
base0B  #ebff87  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #a1efe4  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #62d6e8  Functions, Methods, Attribute IDs, Headings
base0E  #b45bcf  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #00f769  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #e9e9f4;
  background: #282936
}
.hljs::selection,
.hljs ::selection {
  background-color: #4d4f68;
  color: #e9e9f4
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #626483 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #626483
}
/* base04 - #62d6e8 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #62d6e8
}
/* base05 - #e9e9f4 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #e9e9f4
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ea51b2
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #b45bcf
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #00f769
}
.hljs-strong {
  font-weight: bold;
  color: #00f769
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #ebff87
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #a1efe4
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #62d6e8
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #b45bcf
}
.hljs-emphasis {
  color: #b45bcf;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #00f769
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}