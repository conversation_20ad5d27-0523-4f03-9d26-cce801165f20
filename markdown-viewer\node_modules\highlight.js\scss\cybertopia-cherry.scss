pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*
  Theme: <PERSON><PERSON><PERSON> Cherry
  Author: <PERSON> (noraj)
  Repository: https://github.com/noraj/cybertopia-highlightjs
*/
:root {
  --hljs-bg: #101010;
  --hljs-mono-1: #abb2bf;
  --hljs-mono-3: #5c6370;
  --hljs-hue-1: #C50243;
  --hljs-hue-2: #C50253;
  --hljs-hue-3: #C50263;
  --hljs-hue-4: #02c797;
  --hljs-hue-5: #02C584;
  --hljs-hue-6: #02C574;
  --hljs-hue-6-2: #02C563;
  --hljs-hue-7: #C50233
}
.hljs {
  color: var(--hljs-mono-1);
  background: var(--hljs-bg)
}
.hljs-comment,
.hljs-quote,
.hljs-code {
  color: var(--hljs-mono-3);
  font-style: italic
}
.hljs-doctag,
.hljs-keyword,
.hljs-formula,
.hljs-meta .hljs-keyword {
  color: var(--hljs-hue-3)
}
.hljs-section,
.hljs-name,
.hljs-selector-tag,
.hljs-deletion,
.hljs-subst {
  color: var(--hljs-hue-5)
}
.hljs-literal {
  color: var(--hljs-hue-1)
}
.hljs-string,
.hljs-regexp,
.hljs-addition,
.hljs-attribute,
.hljs-meta .hljs-string {
  color: var(--hljs-hue-4)
}
.hljs-attr,
.hljs-variable,
.hljs-template-variable,
.hljs-type,
.hljs-selector-class,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-number,
.hljs-punctuation,
.hljs-variable.language_ {
  color: var(--hljs-hue-6)
}
.hljs-symbol,
.hljs-bullet,
.hljs-link,
.hljs-meta,
.hljs-selector-id,
.hljs-title,
.hljs-title.function_,
.hljs-property,
.hljs-tag,
.hljs-char.escape_ {
  color: var(--hljs-hue-2)
}
.hljs-built_in,
.hljs-title.class_,
.hljs-title.class_.inherited__,
.hljs-class .hljs-title {
  color: var(--hljs-hue-6-2)
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}
.hljs-link {
  text-decoration: underline
}
.hljs-params,
.hljs-operator,
.hljs-template-tag {
  color: var(--hljs-hue-7)
}